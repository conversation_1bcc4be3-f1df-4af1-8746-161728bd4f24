import { useState } from "react";

// Sample client companies data
const clientCompanies = [
  { id: 1, name: "Acme Corp", logo: "/logos/acme.png" },
  { id: 2, name: "TechGiant", logo: "/logos/techgiant.png" },
  { id: 3, name: "Innovate Inc", logo: "/logos/innovate.png" },
  { id: 4, name: "Global Systems", logo: "/logos/globalsystems.png" },
  { id: 5, name: "NextGen", logo: "/logos/nextgen.png" },
  { id: 6, name: "Future Tech", logo: "/logos/futuretech.png" },
  { id: 7, name: "Apex Solutions", logo: "/logos/apex.png" },
  { id: 8, name: "Quantum Industries", logo: "/logos/quantum.png" },
];

// Sample job IDs
const jobIds = [
  { id: "JOB-2023-001", title: "Frontend Developer" },
  { id: "JOB-2023-002", title: "Backend Engineer" },
  { id: "JOB-2023-003", title: "Full Stack Developer" },
  { id: "JOB-2023-004", title: "DevOps Engineer" },
  { id: "JOB-2023-005", title: "UI/UX Designer" },
  { id: "JOB-2023-006", title: "Product Manager" },
  { id: "JOB-2023-007", title: "QA Engineer" },
  { id: "JOB-2023-008", title: "Data Scientist" },
];

// Interface for form data
interface CandidateFormData {
  jobId: string;
  name: string;
  mobile: string;
  email: string;
  client: string;
  profile: string;
  skills: string;
  qualifications: string;
  resume: File | null;
  reasonForJobChange: string;
  currentCompany: string;
  currentJobPosition: string;
  currentJobLocation: string;
  preferredJobLocation: string;
  totalExperienceYears: string;
  totalExperienceMonths: string;
  relevantExperienceYears: string;
  relevantExperienceMonths: string;
  noticePeriod: string; // Added for Serving Notice Period
  joiningOffer: string; // Added for Holding Offer
  linkedinUrl: string; // Added for LinkedIn URL
  remarks: string; // Added for Remarks
}

export function RegisterCandidate() {
  // State for the selected company
  const [selectedCompany, setSelectedCompany] = useState<number | null>(null);

  // State for form data
  const [formData, setFormData] = useState<CandidateFormData>({
    jobId: "",
    name: "",
    mobile: "",
    email: "",
    client: "",
    profile: "",
    skills: "",
    qualifications: "",
    resume: null,
    reasonForJobChange: "",
    currentCompany: "",
    currentJobPosition: "",
    currentJobLocation: "",
    preferredJobLocation: "",
    totalExperienceYears: "0",
    totalExperienceMonths: "0",
    relevantExperienceYears: "0",
    relevantExperienceMonths: "0",
    noticePeriod: "", // Initialize for Serving Notice Period
    joiningOffer: "", // Initialize for Holding Offer
    linkedinUrl: "", // Initialize for LinkedIn URL
    remarks: "", // Initialize for Remarks
  });

  // Handle company selection
  const handleCompanySelect = (companyId: number) => {
    setSelectedCompany(companyId);
    const company = clientCompanies.find((c) => c.id === companyId);
    if (company) {
      setFormData({
        ...formData,
        client: company.name,
      });
    }
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData({
        ...formData,
        resume: e.target.files[0],
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    alert("Candidate registered successfully!");
    setSelectedCompany(null);
    setFormData({
      jobId: "",
      name: "",
      mobile: "",
      email: "",
      client: "",
      profile: "",
      skills: "",
      qualifications: "",
      resume: null,
      reasonForJobChange: "",
      currentCompany: "",
      currentJobPosition: "",
      currentJobLocation: "",
      preferredJobLocation: "",
      totalExperienceYears: "0",
      totalExperienceMonths: "0",
      relevantExperienceYears: "0",
      relevantExperienceMonths: "0",
      noticePeriod: "",
      joiningOffer: "",
      linkedinUrl: "",
      remarks: "",
    });
  };

  // Generate options for years (0-30)
  const yearOptions = Array.from({ length: 31 }, (_, i) => (
    <option key={`year-${i}`} value={i.toString()}>
      {i} {i === 1 ? "Year" : "Years"}
    </option>
  ));

  // Generate options for months (0-11)
  const monthOptions = Array.from({ length: 12 }, (_, i) => (
    <option key={`month-${i}`} value={i.toString()}>
      {i} {i === 1 ? "Month" : "Months"}
    </option>
  ));

  // Options for notice period and joining offer
  const noticePeriodOptions = [
    "None",
    "Immediate",
    "15 Days",
    "30 Days",
    "45 Days",
    "60 Days",
    "90 Days",
  ];

  const joiningOfferOptions = [
    "None",
    "Immediate",
    "15 Days",
    "30 Days",
    "45 Days",
    "60 Days",
  ];

  return (
    <div className="flex flex-col w-full h-full">
      <div className="bg-white p-6 flex-1 w-full overflow-auto">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">
          Register Candidate
        </h2>

        {!selectedCompany ? (
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 w-full">
            <h3 className="text-md font-medium text-gray-700 mb-6">
              Select Client Company
            </h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
              {clientCompanies.map((company) => (
                <div
                  key={company.id}
                  className="border border-gray-200 rounded-lg p-4 flex flex-col items-center hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleCompanySelect(company.id)}
                >
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-3">
                    <span className="text-xl font-bold text-gray-500">
                      {company.name.charAt(0)}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    {company.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 overflow-auto w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-700">
                Register Candidate for{" "}
                {clientCompanies.find((c) => c.id === selectedCompany)?.name}
              </h3>
              <button
                onClick={() => setSelectedCompany(null)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Change Company
              </button>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                {/* Job ID */}
                <div className="space-y-1">
                  <label
                    htmlFor="jobId"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Job ID
                  </label>
                  <select
                    id="jobId"
                    name="jobId"
                    required
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.jobId}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Job ID</option>
                    {jobIds.map((job) => (
                      <option key={job.id} value={job.id}>
                        {job.id} - {job.title}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Name */}
                <div className="space-y-1">
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Mobile */}
                <div className="space-y-1">
                  <label
                    htmlFor="mobile"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Mobile
                  </label>
                  <input
                    type="tel"
                    id="mobile"
                    name="mobile"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.mobile}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Email */}
                <div className="space-y-1">
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Client */}
                <div className="space-y-1">
                  <label
                    htmlFor="client"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Client
                  </label>
                  <input
                    type="text"
                    id="client"
                    name="client"
                    required
                    readOnly
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 sm:text-sm"
                    value={formData.client}
                  />
                </div>

                {/* Profile */}
                <div className="space-y-1">
                  <label
                    htmlFor="profile"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Profile
                  </label>
                  <input
                    type="text"
                    id="profile"
                    name="profile"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.profile}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Skills */}
                <div className="space-y-1">
                  <label
                    htmlFor="skills"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Skills
                  </label>
                  <input
                    type="text"
                    id="skills"
                    name="skills"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.skills}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Qualifications */}
                <div className="space-y-1">
                  <label
                    htmlFor="qualifications"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Qualifications
                  </label>
                  <input
                    type="text"
                    id="qualifications"
                    name="qualifications"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.qualifications}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Resume */}
                <div className="space-y-1 col-span-2">
                  <label
                    htmlFor="resume"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Resume
                  </label>
                  <div className="mt-1 flex items-center">
                    <label
                      htmlFor="resume-upload"
                      className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Upload a file
                      <input
                        id="resume-upload"
                        name="resume-upload"
                        type="file"
                        className="sr-only"
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileChange}
                      />
                    </label>
                    <span className="ml-3 text-sm text-gray-500">
                      {formData.resume
                        ? formData.resume.name
                        : "No file chosen"}
                    </span>
                    {formData.resume && (
                      <button
                        type="button"
                        className="ml-2 text-red-600 hover:text-red-800"
                        onClick={() =>
                          setFormData({ ...formData, resume: null })
                        }
                      >
                        Clear
                      </button>
                    )}
                  </div>
                </div>

                {/* Reason for Job Change */}
                <div className="space-y-1 col-span-2">
                  <label
                    htmlFor="reasonForJobChange"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Reason for Job Change
                  </label>
                  <textarea
                    id="reasonForJobChange"
                    name="reasonForJobChange"
                    rows={2}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.reasonForJobChange}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Current Company */}
                <div className="space-y-1">
                  <label
                    htmlFor="currentCompany"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Current Company
                  </label>
                  <input
                    type="text"
                    id="currentCompany"
                    name="currentCompany"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.currentCompany}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Current Job Position */}
                <div className="space-y-1">
                  <label
                    htmlFor="currentJobPosition"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Current Job Position
                  </label>
                  <input
                    type="text"
                    id="currentJobPosition"
                    name="currentJobPosition"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.currentJobPosition}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Current Job Location */}
                <div className="space-y-1">
                  <label
                    htmlFor="currentJobLocation"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Current Job Location
                  </label>
                  <input
                    type="text"
                    id="currentJobLocation"
                    name="currentJobLocation"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.currentJobLocation}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Preferred Job Location */}
                <div className="space-y-1">
                  <label
                    htmlFor="preferredJobLocation"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Preferred Job Location
                  </label>
                  <input
                    type="text"
                    id="preferredJobLocation"
                    name="preferredJobLocation"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.preferredJobLocation}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Total Experience */}
                <div className="space-y-1">
                  <label
                    htmlFor="totalExperience"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Total Experience
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <select
                      id="totalExperienceYears"
                      name="totalExperienceYears"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={formData.totalExperienceYears}
                      onChange={handleInputChange}
                    >
                      <option value="">Years</option>
                      {yearOptions}
                    </select>
                    <select
                      id="totalExperienceMonths"
                      name="totalExperienceMonths"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={formData.totalExperienceMonths}
                      onChange={handleInputChange}
                    >
                      <option value="">Months</option>
                      {monthOptions}
                    </select>
                  </div>
                </div>

                {/* Relevant Experience */}
                <div className="space-y-1">
                  <label
                    htmlFor="relevantExperience"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Relevant Experience
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <select
                      id="relevantExperienceYears"
                      name="relevantExperienceYears"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={formData.relevantExperienceYears}
                      onChange={handleInputChange}
                    >
                      <option value="">Years</option>
                      {yearOptions}
                    </select>
                    <select
                      id="relevantExperienceMonths"
                      name="relevantExperienceMonths"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={formData.relevantExperienceMonths}
                      onChange={handleInputChange}
                    >
                      <option value="">Months</option>
                      {monthOptions}
                    </select>
                  </div>
                </div>

                {/* Serving Notice Period */}
                <div className="space-y-1">
                  <label
                    htmlFor="noticePeriod"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Serving Notice Period
                  </label>
                  <select
                    id="noticePeriod"
                    name="noticePeriod"
                    required
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.noticePeriod}
                    onChange={handleInputChange}
                  >
                    <option value="">Select</option>
                    {noticePeriodOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Holding Offer */}
                <div className="space-y-1">
                  <label
                    htmlFor="joiningOffer"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Holding Offer
                  </label>
                  <select
                    id="joiningOffer"
                    name="joiningOffer"
                    required
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.joiningOffer}
                    onChange={handleInputChange}
                  >
                    <option value="">Select</option>
                    {joiningOfferOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>

                {/* LinkedIn URL */}
                <div className="space-y-1">
                  <label
                    htmlFor="linkedinUrl"
                    className="block text-sm font-medium text-gray-700"
                  >
                    LinkedIn URL
                  </label>
                  <input
                    type="url"
                    id="linkedinUrl"
                    name="linkedinUrl"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.linkedinUrl}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Remarks */}
                <div className="space-y-1 col-span-2">
                  <label
                    htmlFor="remarks"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Remarks
                  </label>
                  <textarea
                    id="remarks"
                    name="remarks"
                    rows={2}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.remarks}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  onClick={handleSubmit}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Register Candidate
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
