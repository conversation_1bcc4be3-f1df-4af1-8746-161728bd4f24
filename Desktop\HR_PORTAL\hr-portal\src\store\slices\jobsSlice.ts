import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Job interface (copied from the component)
export interface Job {
  id: number;
  budget_max: string;
  budget_min: string;
  client: string;
  contract_in_months: number | null;
  country: string;
  custom_job_type: string | null;
  data_updated_date: string | null;
  data_updated_time: string | null;
  date_created: string;
  detailed_jd: string;
  experience_max: string;
  experience_min: string;
  jd_pdf_extension: string | null;
  jd_pdf_present: boolean;
  job_status: "Active" | "Closed" | "On Hold";
  job_type: string;
  location: string;
  management: string;
  mode: string;
  no_of_positions: string;
  notice_period: string;
  recruiter: string;
  role: string;
  shift_timings: string;
  skills: string;
  time_created: string;
}

// API Response interface
export interface JobsApiResponse {
  job_posts_active: Job[];
  job_posts_closed: Job[];
  job_posts_hold: Job[];
  user_name: string;
}

// Filter tag interface
export interface FilterTag {
  column: string;
  value: string;
}

// Jobs state interface
export interface JobsState {
  jobs: Job[];
  loading: boolean;
  error: string | null;
  lastFetched: string | null;
  // Filter and search state
  searchTags: FilterTag[];
  appliedTags: FilterTag[];
  dateFilter: number | null;
  appliedCustomDateRange: { start: string; end: string } | null;
  // Pagination state
  currentPage: number;
  itemsPerPage: number;
  // Sorting state
  sortConfig: {
    key: keyof Job | "posted_by" | null;
    direction: "ascending" | "descending" | null;
  };
}

// Initial state
const initialState: JobsState = {
  jobs: [],
  loading: false,
  error: null,
  lastFetched: null,
  searchTags: [],
  appliedTags: [],
  dateFilter: null,
  appliedCustomDateRange: null,
  currentPage: 1,
  itemsPerPage: 10,
  sortConfig: { key: null, direction: null },
};

// Async thunk for fetching jobs
export const fetchJobs = createAsyncThunk(
  'jobs/fetchJobs',
  async (params: { username: string; force?: boolean }, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { jobs: JobsState };
      const { lastFetched, jobs } = state.jobs;

      // Check cache validity (5 minutes)
      const CACHE_DURATION = 5 * 60 * 1000;
      const isCacheValid = lastFetched &&
        (new Date().getTime() - new Date(lastFetched).getTime()) < CACHE_DURATION;

      // Don't fetch if cache is valid and not forced
      if (!params.force && isCacheValid && jobs.length > 0) {
        console.log('Using cached jobs data');
        return { jobs, fromCache: true };
      }

      console.log('Fetching jobs from API...');

      const API_BASE_URL = "https://backend.makonissoft.com";
      const requestBody = { username: params.username };
      const response = await fetch(`${API_BASE_URL}/view_all_jobs`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data: JobsApiResponse = await response.json();
      const allJobs = [...data.job_posts_active, ...data.job_posts_closed, ...data.job_posts_hold];

      console.log(`Fetched ${allJobs.length} jobs`);

      return { jobs: allJobs, fromCache: false };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "An unknown error occurred");
    }
  }
);

// Jobs slice
const jobsSlice = createSlice({
  name: 'jobs',
  initialState,
  reducers: {
    // Search and filter actions
    addSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const newTag = action.payload;
      if (!state.searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
        state.searchTags.push(newTag);
      }
    },
    removeSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const tagToRemove = action.payload;
      state.searchTags = state.searchTags.filter(
        tag => tag.value !== tagToRemove.value || tag.column !== tagToRemove.column
      );
    },
    applyFilters: (state) => {
      state.appliedTags = [...state.searchTags];
      state.dateFilter = null;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    clearAllFilters: (state) => {
      state.searchTags = [];
      state.appliedTags = [];
      state.dateFilter = null;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    setDateFilter: (state, action: PayloadAction<number | null>) => {
      state.appliedTags = [];
      state.searchTags = [];
      state.dateFilter = action.payload;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    setCustomDateRange: (state, action: PayloadAction<{ start: string; end: string }>) => {
      state.appliedCustomDateRange = action.payload;
      state.dateFilter = null;
      state.appliedTags = [];
      state.searchTags = [];
      state.currentPage = 1;
    },
    // Pagination actions
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.itemsPerPage = action.payload;
      state.currentPage = 1;
    },
    // Sorting actions
    setSortConfig: (state, action: PayloadAction<{
      key: keyof Job | "posted_by" | null;
      direction: "ascending" | "descending" | null;
    }>) => {
      state.sortConfig = action.payload;
    },
    // Cache management
    clearJobsCache: (state) => {
      state.jobs = [];
      state.lastFetched = null;
      state.error = null;
    },
    refreshJobs: (state) => {
      state.lastFetched = null; // This will force a refresh on next fetch
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchJobs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchJobs.fulfilled, (state, action) => {
        state.loading = false;
        if (!action.payload.fromCache) {
          state.jobs = action.payload.jobs;
          state.lastFetched = new Date().toISOString();
        }
        state.error = null;
      })
      .addCase(fetchJobs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setDateFilter,
  setCustomDateRange,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
  clearJobsCache,
  refreshJobs,
} = jobsSlice.actions;

export default jobsSlice.reducer;
