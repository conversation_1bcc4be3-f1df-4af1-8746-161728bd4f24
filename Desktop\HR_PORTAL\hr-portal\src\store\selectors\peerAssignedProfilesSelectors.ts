import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { PeerAssignedProfile, FilterTag } from '../slices/peerAssignedProfilesSlice';

// Base selectors
export const selectProfiles = (state: RootState) => state.peerAssignedProfiles.profiles;
export const selectRawCandidates = (state: RootState) => state.peerAssignedProfiles.rawCandidates;
export const selectProfilesLoading = (state: RootState) => state.peerAssignedProfiles.loading;
export const selectProfilesError = (state: RootState) => state.peerAssignedProfiles.error;
export const selectSearchTags = (state: RootState) => state.peerAssignedProfiles.searchTags;
export const selectAppliedTags = (state: RootState) => state.peerAssignedProfiles.appliedTags;
export const selectDateFilter = (state: RootState) => state.peerAssignedProfiles.dateFilter;
export const selectAppliedCustomDateRange = (state: RootState) => state.peerAssignedProfiles.appliedCustomDateRange;
export const selectCurrentPage = (state: RootState) => state.peerAssignedProfiles.currentPage;
export const selectItemsPerPage = (state: RootState) => state.peerAssignedProfiles.itemsPerPage;
export const selectSortConfig = (state: RootState) => state.peerAssignedProfiles.sortConfig;

// Column definitions
export const profileColumns = [
  { key: "priority", label: "Priority", sortable: true },
  { key: "date", label: "Date", sortable: true },
  { key: "viewJD", label: "View JD", sortable: false },
  { key: "name", label: "Name", sortable: true },
  { key: "client", label: "Client", sortable: true },
  { key: "profile", label: "Profile", sortable: true },
  { key: "skills", label: "Skills", sortable: true },
  { key: "recruiter", label: "Recruiter", sortable: true },
  { key: "reviewStatus", label: "Review Status", sortable: true },
  { key: "resume", label: "Resume", sortable: false },
  { key: "details", label: "Details", sortable: false },
] as const;

// Suggestion priority for search
export const suggestionPriority: (keyof PeerAssignedProfile)[] = [
  'name',
  'client',
  'profile',
  'skills',
  'recruiter',
  'reviewStatus',
  'priority',
  'date',
];

// Memoized selector for filtered profiles
export const selectFilteredProfiles = createSelector(
  [selectProfiles, selectAppliedTags, selectDateFilter, selectAppliedCustomDateRange],
  (profiles, appliedTags, dateFilter, appliedCustomDateRange) => {
    const groupedTags = appliedTags.reduce((acc, tag) => {
      const key = tag.column;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(tag);
      return acc;
    }, {} as Record<string, FilterTag[]>);

    return profiles.filter((profile) => {
      // Date Filter Logic
      const profileDate = new Date(profile.date);
      if (isNaN(profileDate.getTime())) return false;
      
      if (dateFilter) {
        const now = new Date();
        const pastDate = new Date(now.setDate(now.getDate() - dateFilter));
        if (profileDate < pastDate) return false;
      }
      
      if (appliedCustomDateRange) {
        const startDate = new Date(appliedCustomDateRange.start);
        const endDate = new Date(appliedCustomDateRange.end);
        endDate.setDate(endDate.getDate() + 1);
        if (profileDate < startDate || profileDate >= endDate) return false;
      }

      // Tag-based Search Logic
      if (appliedTags.length > 0) {
        const matchesAllGroups = Object.values(groupedTags).every(tagGroup => {
          return tagGroup.some(tag => {
            const tagValue = tag.value.toLowerCase();
            const columnInfo = profileColumns.find(c => c.label === tag.column);

            if (tag.column === 'Any') {
              return Object.values(profile).some(val => String(val).toLowerCase().includes(tagValue));
            }
            if (!columnInfo) return false;

            const profileValue = profile[columnInfo.key as keyof PeerAssignedProfile];
            return String(profileValue).toLowerCase().includes(tagValue);
          });
        });

        if (!matchesAllGroups) {
          return false;
        }
      }

      return true;
    });
  }
);

// Memoized selector for sorted profiles
export const selectSortedProfiles = createSelector(
  [selectFilteredProfiles, selectSortConfig],
  (filteredProfiles, sortConfig) => {
    return [...filteredProfiles].sort((a, b) => {
      if (!sortConfig.key) return 0;
      
      let aValue: any = a[sortConfig.key];
      let bValue: any = b[sortConfig.key];
      
      // Handle date sorting
      if (sortConfig.key === 'date') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }
      
      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }
);

// Memoized selector for paginated profiles
export const selectPaginatedProfiles = createSelector(
  [selectSortedProfiles, selectCurrentPage, selectItemsPerPage],
  (sortedProfiles, currentPage, itemsPerPage) => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return sortedProfiles.slice(indexOfFirstItem, indexOfLastItem);
  }
);

// Selector for search suggestions
export const selectSearchSuggestions = createSelector(
  [selectFilteredProfiles, (state: RootState, inputValue: string) => inputValue],
  (filteredProfiles, inputValue) => {
    if (!inputValue) return [];
    
    const suggestions: { value: string; column: string }[] = [];
    
    // Use suggestion priority to order results
    suggestionPriority.forEach(key => {
      const column = profileColumns.find(c => c.key === key);
      if (!column) return;
      
      filteredProfiles.forEach(profile => {
        const value = profile[key];
        if (typeof value === 'string' && value.toLowerCase().includes(inputValue.toLowerCase())) {
          if (!suggestions.some(s => s.value === value && s.column === column.label)) {
            suggestions.push({ value, column: column.label });
          }
        }
      });
    });
    
    return suggestions.slice(0, 7);
  }
);

// Export columns for use in components
