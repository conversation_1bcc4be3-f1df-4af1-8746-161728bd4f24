import { useState, useEffect, useRef } from "react";
import {
  Search,
  Calendar,
  MessageSquare,
  FileText,
  Info,
  RefreshCw,
  Edit,
  Paperclip,
  Trash2,
  Columns,
  ChevronDown,
  Download,
  Share2,
  X,
  Check,
} from "lucide-react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { useDebounce } from "@/hooks/use-debounce";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { useUser } from "@/contexts/user-context";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchCandidates,
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setDateFilter,
  setCustomDateRange,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
  toggleRowSelection,
  setSelectAll,
  clearSelection,
  toggleColumnVisibility,
  type FilterTag,
} from "@/store/slices/candidatesSlice";
import {
  selectCandidatesLoading,
  selectCandidatesError,
  selectSearchTags,
  selectAppliedTags,
  selectDateFilter,
  selectAppliedCustomDateRange,
  selectCurrentPage,
  selectItemsPerPage,
  selectSortConfig,
  selectSelectedRows,
  selectSelectAll,
  selectVisibleColumns,
  selectFilteredCandidates,
  selectPaginatedCandidates,
  selectFilteredColumns,
  selectSearchSuggestions,
  candidateColumns,
  suggestionPriority,
} from "@/store/selectors/candidatesSelectors";
import { type Candidate } from "@/types/candidate";
import { CandidateDetailsModal } from "@/components/modals/candidate-details-modal";
import { ScheduleMeetingModal } from "@/components/modals/schedule-meeting-modal";
import { EditCandidateModal } from "@/components/modals/edit-candidate-modal";
import { UpdateCandidateModal } from "@/components/modals/update-candidate-modal";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { openWhatsAppChat, WhatsAppTemplates } from "@/utils/whatsapp";
import { KebabMenu, createKebabMenuItem, type KebabMenuItem } from "@/components/ui/kebab-menu";


// Constants are now imported from Redux selectors

export function DashboardCandidateTable() {
  const { userEmail, userRole, userName, userId } = useUser();
  const dispatch = useAppDispatch();

  // Redux state
  const loading = useAppSelector(selectCandidatesLoading);
  const error = useAppSelector(selectCandidatesError);
  const searchTags = useAppSelector(selectSearchTags);
  const appliedTags = useAppSelector(selectAppliedTags);
  const dateFilter = useAppSelector(selectDateFilter);
  const appliedCustomDateRange = useAppSelector(selectAppliedCustomDateRange);
  const currentPage = useAppSelector(selectCurrentPage);
  const itemsPerPage = useAppSelector(selectItemsPerPage);
  const sortConfig = useAppSelector(selectSortConfig);
  const selectedRows = useAppSelector(selectSelectedRows);
  const selectAll = useAppSelector(selectSelectAll);
  const visibleColumns = useAppSelector(selectVisibleColumns);
  const filteredCandidates = useAppSelector(selectFilteredCandidates);
  const currentCandidates = useAppSelector(selectPaginatedCandidates);
  const filteredColumns = useAppSelector(selectFilteredColumns);

  // Local UI state
  const [inputValue, setInputValue] = useState('');
  const debouncedInputValue = useDebounce(inputValue, 250);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState<string>('');
  const [customEndDate, setCustomEndDate] = useState<string>('');

  // Refs
  const searchContainerRef = useRef<HTMLDivElement>(null);

  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const dateButtonRef = useRef<HTMLButtonElement>(null);

  // Get search suggestions from Redux
  const suggestions = useAppSelector(state => selectSearchSuggestions(state, debouncedInputValue));

  // Fetch candidates on component mount
  useEffect(() => {
    if (userId && userRole && userName) {
      const userType = userRole === "manager" ? "management" : "recruiter";
      dispatch(fetchCandidates({ userId, userType, userName }));
    }
  }, [dispatch, userId, userRole, userName]);

  // State for columns dropdown
  const [isColumnsDropdownOpen, setIsColumnsDropdownOpen] = useState(false);
  const columnsDropdownRef = useRef<HTMLDivElement>(null);
  const columnsButtonRef = useRef<HTMLButtonElement>(null);

  // Row selection is now managed by Redux

  // State for share modal
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [shareSelectedCandidates, setShareSelectedCandidates] = useState<Candidate[]>([]);
  const [selectedFields, setSelectedFields] = useState<Set<string>>(new Set());
  const [selectAllFields, setSelectAllFields] = useState(false);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [emailForm, setEmailForm] = useState({
    subject: '',
    body: '',
    email: ''
  });

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // --- NEW: EFFECT TO CLOSE SUGGESTIONS ON OUTSIDE CLICK ---
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setInputValue(''); // Clear input instead of suggestions since suggestions come from Redux
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [searchContainerRef]);


  // Close columns dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        columnsDropdownRef.current &&
        !columnsDropdownRef.current.contains(event.target as Node) &&
        columnsButtonRef.current &&
        !columnsButtonRef.current.contains(event.target as Node)
      ) {
        setIsColumnsDropdownOpen(false);
      }
    }

    if (isColumnsDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isColumnsDropdownOpen]);

  // Toggle column visibility
  const handleToggleColumnVisibility = (columnKey: keyof Candidate) => {
    dispatch(toggleColumnVisibility(columnKey));
  };

  // Selection helper functions
  const handleSelectAll = (checked: boolean) => {
    dispatch(setSelectAll(checked));
  };

  const handleSelectRow = (candidateId: number, checked: boolean) => {
    dispatch(toggleRowSelection(candidateId));
  };

  // Download selected data
  const handleDownloadSelected = () => {
    const selectedCandidates = currentCandidates.filter(candidate =>
      selectedRows.includes(candidate.id)
    );

    if (selectedCandidates.length === 0) {
      alert('Please select at least one row to download.');
      return;
    }

    // Convert to CSV format
    const headers = filteredColumns.map(col => col.label).join(',');
    const csvData = selectedCandidates.map(candidate =>
      filteredColumns.map(col => {
        const value = candidate[col.key];
        // Escape commas and quotes in CSV
        return typeof value === 'string' && (value.includes(',') || value.includes('"'))
          ? `"${value.replace(/"/g, '""')}"`
          : String(value);
      }).join(',')
    ).join('\n');

    const csvContent = `${headers}\n${csvData}`;

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `candidates_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Share functionality
  const handleShareSelected = () => {
    const selectedCandidates = currentCandidates.filter(candidate =>
      selectedRows.includes(candidate.id)
    );

    if (selectedCandidates.length === 0) {
      alert('Please select at least one row to share.');
      return;
    }

    setShareSelectedCandidates(selectedCandidates);
    setIsShareModalOpen(true);
  };

  // Get all available fields from candidate data
  const getAllCandidateFields = () => {
    if (shareSelectedCandidates.length === 0) return [];

    // Get all unique keys from all selected candidates
    const allFields = new Set<string>();
    shareSelectedCandidates.forEach(candidate => {
      Object.keys(candidate).forEach(key => allFields.add(key));
    });

    return Array.from(allFields).sort();
  };

  // Handle field selection
  const handleFieldToggle = (field: string) => {
    const newSelectedFields = new Set(selectedFields);
    if (newSelectedFields.has(field)) {
      newSelectedFields.delete(field);
    } else {
      newSelectedFields.add(field);
    }
    setSelectedFields(newSelectedFields);
    setSelectAllFields(newSelectedFields.size === getAllCandidateFields().length);
  };

  // Handle select all fields
  const handleSelectAllFields = (checked: boolean) => {
    setSelectAllFields(checked);
    if (checked) {
      setSelectedFields(new Set(getAllCandidateFields()));
    } else {
      setSelectedFields(new Set());
    }
  };

  // Handle share via email
  const handleShareViaEmail = () => {
    if (selectedFields.size === 0) {
      alert('Please select at least one field to share.');
      return;
    }
    setIsEmailModalOpen(true);
  };

  // Handle email send
  const handleEmailSend = () => {
    if (!emailForm.email || !emailForm.subject) {
      alert('Please fill in email and subject fields.');
      return;
    }

    // Create email content with selected fields
    const emailContent = shareSelectedCandidates.map(candidate => {
      const selectedData: Record<string, any> = {};
      selectedFields.forEach(field => {
        if (candidate[field as keyof Candidate] !== undefined) {
          selectedData[field] = candidate[field as keyof Candidate];
        }
      });
      return selectedData;
    });

    // Create mailto link
    const subject = encodeURIComponent(emailForm.subject);
    const body = encodeURIComponent(
      `${emailForm.body}\n\nCandidate Details:\n${JSON.stringify(emailContent, null, 2)}`
    );
    const mailtoLink = `mailto:${emailForm.email}?subject=${subject}&body=${body}`;

    window.open(mailtoLink);

    // Reset and close modals
    setIsEmailModalOpen(false);
    setIsShareModalOpen(false);
    setEmailForm({ subject: '', body: '', email: '' });
    setSelectedFields(new Set());
    setSelectAllFields(false);
  };

  // Modal states
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isMeetingModalOpen, setIsMeetingModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  // Function to open candidate details modal
  const openCandidateDetails = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsDetailsModalOpen(true);
  };

  // Function to close candidate details modal
  const closeCandidateDetails = () => {
    setSelectedCandidate(null);
    setIsDetailsModalOpen(false);
  };

  // Function to open schedule meeting modal
  const openScheduleMeeting = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsMeetingModalOpen(true);
  };

  // Function to close schedule meeting modal
  const closeScheduleMeeting = () => {
    setSelectedCandidate(null);
    setIsMeetingModalOpen(false);
  };

  // Function to open edit candidate modal
  const openEditCandidate = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsEditModalOpen(true);
  };

  // Function to close edit candidate modal
  const closeEditCandidate = () => {
    setSelectedCandidate(null);
    setIsEditModalOpen(false);
  };

  // Function to handle candidate update
  const handleCandidateUpdate = (updatedCandidate: Candidate) => {
    // Here you would typically update the candidate in your data store
    console.log('Updated candidate:', updatedCandidate);
    // For now, we'll just close the modal
    closeEditCandidate();
  };

  // Function to open update candidate status modal
  const openUpdateCandidate = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsUpdateModalOpen(true);
  };

  // Function to close update candidate status modal
  const closeUpdateCandidate = () => {
    setSelectedCandidate(null);
    setIsUpdateModalOpen(false);
  };

  // Function to handle candidate status update
  const handleCandidateStatusUpdate = (candidateId: string, status: string, comment: string) => {
    // Here you would typically update the candidate status in your data store
    console.log('Updated candidate status:', { candidateId, status, comment });
    // For now, we'll just close the modal
    closeUpdateCandidate();
  };

  // Function to open WhatsApp chat
  const openWhatsAppWithCandidate = (candidate: Candidate) => {
    if (candidate.phone) {
      const message = WhatsAppTemplates.general(`${candidate.firstName} ${candidate.lastName}`);
      openWhatsAppChat(candidate.phone, message);
    } else {
      alert('No phone number available for this candidate');
    }
  };

  // Create kebab menu items for each candidate
  const createCandidateMenuItems = (candidate: Candidate): KebabMenuItem[] => [
    createKebabMenuItem("schedule-meeting", "Schedule Meeting", () => openScheduleMeeting(candidate), { icon: Calendar }),
    createKebabMenuItem("whatsapp-chat", "Chat on WhatsApp", () => openWhatsAppWithCandidate(candidate), { icon: MessageSquare }),
    createKebabMenuItem("view-resume", "View Resume", () => alert('Resume viewing functionality to be implemented'), { icon: FileText }),
    createKebabMenuItem("view-details", "View Details", () => openCandidateDetails(candidate), { icon: Info, separator: true }),
    createKebabMenuItem("update-status", "Update Status", () => openUpdateCandidate(candidate), { icon: RefreshCw }),
    createKebabMenuItem("edit-candidate", "Edit Candidate", () => openEditCandidate(candidate), { icon: Edit }),
    createKebabMenuItem("additional-files", "Additional Files", () => alert('Additional Files functionality to be implemented'), { icon: Paperclip, separator: true }),
    createKebabMenuItem("delete", "Delete", () => {
      if (window.confirm('Are you sure you want to delete this candidate?')) {
        alert('Delete functionality to be implemented');
      }
    }, { icon: Trash2, variant: "destructive" }),
  ];

  // Get status badge color
  const getStatusColor = (status: Candidate["status"]) => {
    switch (status) {
      case "New": return "bg-blue-100 text-blue-800";
      case "Screening": return "bg-purple-100 text-purple-800";
      case "Interview": return "bg-yellow-100 text-yellow-800";
      case "Offer": return "bg-orange-100 text-orange-800";
      case "Hired": return "bg-green-100 text-green-800";
      case "Rejected": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  // Filtering, sorting, and pagination are now handled by Redux selectors

  // Pagination is now handled by Redux selectors
  // Calculate total pages
  const totalPages = Math.ceil(filteredCandidates.length / itemsPerPage);

  // Ensure current page is valid
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      dispatch(setCurrentPage(1));
    }
  }, [dispatch, currentPage, totalPages]);

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    dispatch(setCurrentPage(pageNumber));
  };

  // Handle sort with animation
  const handleSort = async (key: keyof Candidate) => {
    await animateSorting();
    let direction: "ascending" | "descending" | null = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (sortConfig.key === key && sortConfig.direction === "descending") {
      direction = null;
    }
    dispatch(setSortConfig({ key, direction }));
  };

  const handleAddTag = (tagOrSuggestion: string | { value: string; column: string }) => {
    let newTag: FilterTag;

    if (typeof tagOrSuggestion === 'string') {
      newTag = { value: tagOrSuggestion.trim(), column: 'Any' };
    } else {
      const value = tagOrSuggestion.value || '';
      newTag = { value: value.trim(), column: tagOrSuggestion.column };
    }
    if (newTag.value && !searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
      dispatch(addSearchTag(newTag));
    }
    setInputValue('');
  };
  const handleRemoveTag = (tagToRemove: FilterTag) => {
    dispatch(removeSearchTag(tagToRemove));
  };

  const handleApplyFilters = () => {
    dispatch(applyFilters());
  };

  const handleClearAllFilters = () => {
    dispatch(clearAllFilters());
    setInputValue('');
    setCustomStartDate('');
    setCustomEndDate('');
  };
  // Define the date filter options
  const dateFilterOptions = [
    { value: null, label: "All Time" },
    { value: 1, label: "Last 24 Hours" },
    { value: 7, label: "Last 7 Days" },
    { value: 30, label: "Last 30 Days" },
    // { value: "custom", label: "Custom Range" }
  ];

  // Add effect to close date dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dateDropdownRef.current &&
        !dateDropdownRef.current.contains(event.target as Node) &&
        dateButtonRef.current &&
        !dateButtonRef.current.contains(event.target as Node)
      ) {
        setIsDateDropdownOpen(false);
      }
    }

    if (isDateDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isDateDropdownOpen]);

  const handleDateFilter = (days: number | null) => {
    dispatch(setDateFilter(days));
    setInputValue('');
    setIsDateDropdownOpen(false);
    setCustomStartDate('');
    setCustomEndDate('');
  };

  // Suggestion generation is now handled by Redux selectors


  // Get the current date filter label for display
  const getCurrentDateFilterLabel = () => {
    if (appliedCustomDateRange) {
      const startDate = new Date(appliedCustomDateRange.start).toLocaleDateString();
      const endDate = new Date(appliedCustomDateRange.end).toLocaleDateString();
      return `${startDate} - ${endDate}`;
    }
    const option = dateFilterOptions.find(opt => opt.value === dateFilter);
    return option ? option.label : "Select Date";
  };

  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      dispatch(setCustomDateRange({
        start: customStartDate,
        end: customEndDate
      }));
      setIsDateDropdownOpen(false);
    } else {
      alert('Please select both start and end dates');
    }
  };



  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };


  return (
    <div className="flex flex-col">
      {/* --- NEW SEARCH BAR UI --- */}
      <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
        {/* --- SEARCH TAGS & INPUT --- */}
        <div className="flex-1 min-w-[300px]  relative z-[11]" ref={searchContainerRef}>
          <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
            <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
            {searchTags.map(tag => (
              <span key={`${tag.column}-${tag.value}`}
                className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800"/* ... */>
                {/* Optionally show the column for clarity */}
                <span className="font-normal text-blue-600 mr-1">{tag.column}:</span>
                {tag.value}
                <button
                  onClick={() => handleRemoveTag(tag)}
                // ...
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </span>
            ))}
            <input
              id="search-tag-input"
              type="text"
              className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none"
              placeholder="Search and add filters..."
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={(e) => {
                if (e.key === "Enter" && inputValue.trim()) {
                  e.preventDefault();
                  handleAddTag({ value: inputValue, column: 'Any' });
                }
              }}
            />
          </div>
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
            <button onClick={handleApplyFilters} title="Apply Filters" className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600 focus:outline-none">
              <Check className="h-5 w-5" />
            </button>
            <button onClick={handleClearAllFilters} title="Clear All Filters" className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500 focus:outline-none">
              <X className="h-5 w-5" />
            </button>
          </div>
          {suggestions.length > 0 && (
            <div className="absolute z-[9999] w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {suggestions.map((s, i) => (
                <div
                  key={`${s.value}-${i}`}
                  className="cursor-pointer p-3 hover:bg-blue-50 hover:border-l-4 hover:border-l-blue-500 flex justify-between items-center text-sm transition-all duration-150"
                  onClick={() => handleAddTag(s)}
                >
                  <span className="font-medium text-gray-900">{s.value}</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md font-medium">{s.column}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* --- QUICK FILTERS & CONTROLS --- */}
        <div className="flex flex-wrap items-center gap-3">
          {/* <span className="text-sm font-medium text-gray-600">Date Filter:</span> */}

          {/* Date Filter Dropdown */}
          <div className="relative">
            <button
              ref={dateButtonRef}
              onClick={() => setIsDateDropdownOpen(!isDateDropdownOpen)}
              className="bg-white border border-gray-300 text-gray-900 px-2 py-2.5 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-50 focus:ring-blue-500 focus:border-blue-500 min-w-[140px] justify-between"
            >
              <div className="flex items-center gap-2">
                <Calendar className="h-3.5 w-3.5" />
                <span>{getCurrentDateFilterLabel()}</span>
              </div>
              <ChevronDown className={`h-3.5 w-3.5 transition-transform ${isDateDropdownOpen ? 'rotate-180' : ''}`} />
            </button>

            {isDateDropdownOpen && (
              <div
                ref={dateDropdownRef}
                className="absolute right-0 top-full mt-1 z-[9999] bg-white rounded-lg shadow-xl border border-gray-200 min-w-[180px] py-2 animate-in fade-in-0 zoom-in-95 duration-200"
              >
                <div className="px-2 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-200">
                  Filter by Date
                </div>
                {dateFilterOptions.map((option) => (
                  <button
                    key={option.value || 'all'}
                    onClick={() => handleDateFilter(option.value)}
                    className={`w-full text-left px-2 py-2 hover:bg-gray-50 text-sm transition-colors duration-150 flex items-center justify-between ${dateFilter === option.value
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-r-blue-500'
                      : 'text-gray-700'
                      }`}
                  >
                    <span>{option.label}</span>
                    {dateFilter === option.value && (
                      <Check className="h-3.5 w-3.5 text-blue-600" />
                    )}
                  </button>
                ))}

                {/* Custom Date Range Section - Always visible */}
                <div className="border-t border-gray-200 p-3 bg-gray-50">
                  <div className="space-y-3">
                    <div className="text-xs font-semibold text-gray-600 mb-2">Or Select Custom Date Range</div>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Start Date</label>
                        <input
                          type="date"
                          className="w-full border border-gray-300 px-2 py-1 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          value={customStartDate}
                          onChange={(e) => setCustomStartDate(e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">End Date</label>
                        <input
                          type="date"
                          className="w-full border border-gray-300 px-2 py-1 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          value={customEndDate}
                          onChange={(e) => setCustomEndDate(e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="flex gap-2 justify-end">
                      <button
                        onClick={() => {
                          setCustomStartDate('');
                          setCustomEndDate('');
                          setAppliedCustomDateRange(null);
                          if (!dateFilter) {
                            // If no preset filter is selected, close the dropdown
                            setIsDateDropdownOpen(false);
                          }
                        }}
                        className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-100"
                      >
                        Clear
                      </button>
                      <button
                        onClick={handleCustomDateApply}
                        className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                        disabled={!customStartDate || !customEndDate}
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

          </div>

          {/* Column Toggle Dropdown */}
          <div className="relative">
            <button
              ref={columnsButtonRef}
              onClick={() => setIsColumnsDropdownOpen(!isColumnsDropdownOpen)}
              className="bg-white border border-gray-300 text-gray-900 px-2 py-2.5 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
            >
              <Columns className="h-3.5 w-3.5" />
              Columns
              <ChevronDown className={`h-3.5 w-3.5 transition-transform ${isColumnsDropdownOpen ? 'rotate-180' : ''}`} />
            </button>
            {isColumnsDropdownOpen && (
              <div
                ref={columnsDropdownRef}
                className="absolute right-0 top-full mt-1 z-[9999] bg-white rounded-lg shadow-xl border border-gray-200 min-w-[200px] py-2 animate-in fade-in-0 zoom-in-95 duration-200"
              >
                <div className="px-2 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-200">
                  Show/Hide Columns
                </div>
                {candidateColumns.map((column) => (
                  <label
                    key={String(column.key)}
                    className="flex items-center gap-3 px-2 py-2 hover:bg-gray-50 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={visibleColumns.includes(column.key)}
                      onChange={() => handleToggleColumnVisibility(column.key)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{column.label}</span>
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Download / Share / Pagination */}
          <button
            onClick={handleDownloadSelected}
            disabled={selectedRows.length === 0}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-2 py-2.5 rounded-md text-sm font-medium flex items-center gap-2"
            title={`Download selected rows (${selectedRows.length} selected)`}
          >
            <Download className="h-3.5 w-3.5" />
            Download({selectedRows.length})
          </button>
          <button
            onClick={handleShareSelected}
            disabled={selectedRows.length === 0}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-2 py-2.5 rounded-md text-sm font-medium flex items-center gap-2"
            title={`Share selected rows (${selectedRows.length} selected)`}
          >
            Share<Share2 className="h-3.5 w-3.5" />
            ({selectedRows.length})
          </button>
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              const newItemsPerPage = Number(e.target.value);
              await animatePagination();
              dispatch(setItemsPerPage(newItemsPerPage));
            }}
            value={itemsPerPage}
          >
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-center">
            <Info className="h-3.5 w-3.5 text-yellow-600 mr-2" />
            <p className="text-sm text-yellow-800">{error}</p>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {loading && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-center">
            <RefreshCw className="h-3.5 w-3.5 text-blue-600 mr-2 animate-spin" />
            <p className="text-sm text-blue-800">Loading candidates...</p>
          </div>
        </div>
      )}

      {/* Table with fixed header and scrollable body */}
      <AnimatedTableWrapper
        isLoading={isLoading || loading}
        loadingComponent={<TableSkeleton rows={10} cols={filteredColumns.length + 2} />}
        className="border border-gray-200 rounded-md overflow-hidden flex-1"
      >
        <div className="overflow-auto h-[490px]" style={{ scrollbarGutter: 'stable' }}>
          <table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {/* Select All Checkbox */}
                <th className="sticky top-0 z-10 px-2 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-12">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                {filteredColumns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className={`sticky top-0 z-10 px-2 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200 ${column.key === "comment" ? "w-36" :
                      column.key === "skills" ? "w-24" :
                        column.key === "email" ? "w-36" :
                          column.key === "firstName" ? "w-28" :
                            column.key === "peerReviewer" || column.key === "recruiter" ? "w-28" :
                              "w-24"

                      }`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction === "ascending"
                              ? "ascending"
                              : "descending"
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                {/* Action Column Headers */}
                <th className="sticky top-0 z-10 px-6 py-3 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-16">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentCandidates.length > 0 ? (
                currentCandidates.map((candidate, index) => (
                  <AnimatedTableRow key={candidate.id} index={index}>
                    {/* Row Selection Checkbox */}
                    <td className="px-2 py-2 whitespace-nowrap text-center">
                      <input
                        type="checkbox"
                        checked={selectedRows.includes(candidate.id)}
                        onChange={(e) => handleSelectRow(candidate.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    {filteredColumns.map((column) => (
                      <td
                        key={`${candidate.id}-${String(column.key)}`}
                        className={`px-2 py-2 text-xs text-gray-800 font-medium ${column.key === "comment" || column.key === "skills" ? "max-w-xs truncate" : "whitespace-nowrap"
                          }`}
                      >
                        {column.key === "status" ? (
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${getStatusColor(
                              candidate.status
                            )}`}
                          >
                            {candidate.status}
                          </span>
                        ) : column.key === "firstName" ? (
                          <span className="font-semibold text-green-700">
                            {candidate.firstName} {candidate.lastName}
                          </span>
                        ) : column.key === "comment" ? (
                          <span title={candidate.comment} className="truncate block font-medium">
                            {candidate.comment}
                          </span>
                        ) : column.key === "skills" ? (
                          <span title={candidate.skills} className="truncate block font-medium">
                            {candidate.skills}
                          </span>
                        ) : column.key === "peerReviewer" ? (
                          <span className="font-semibold text-blue-600">
                            {candidate.peerReviewer}
                          </span>
                        ) : column.key === "recruiter" ? (
                          <span className="font-semibold text-green-600">
                            {candidate.recruiter}
                          </span>
                        ) : (
                          String(candidate[column.key])
                        )}
                      </td>
                    ))}

                    {/* Actions Column */}
                    <td className="px-2 py-2 whitespace-nowrap text-sm text-gray-500 w-16">
                      <div className="flex items-center justify-center">
                        <KebabMenu items={createCandidateMenuItems(candidate)} />
                      </div>
                    </td>


                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={
                      filteredColumns.length + 2
                    } /* Add 1 for the actions column and 1 for the checkbox column */
                    className="px-2 py-2 text-center text-sm text-gray-500"
                  >
                    No candidates found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>
          {filteredCandidates.length > 0 ? (
            <>
              Showing {Math.min(1 + (currentPage - 1) * itemsPerPage, filteredCandidates.length)} to{" "}
              {Math.min(currentPage * itemsPerPage, filteredCandidates.length)} of{" "}
              {filteredCandidates.length} candidates
            </>
          ) : (
            "No candidates found"
          )}
        </div>
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={paginate}
        />
      </div>

      {/* Candidate Details Modal */}
      <CandidateDetailsModal
        candidate={selectedCandidate}
        isOpen={isDetailsModalOpen}
        onClose={closeCandidateDetails}
      />

      {/* Schedule Meeting Modal */}
      <ScheduleMeetingModal
        candidate={selectedCandidate}
        isOpen={isMeetingModalOpen}
        onClose={closeScheduleMeeting}
      />

      {/* Edit Candidate Modal */}
      <EditCandidateModal
        candidate={selectedCandidate}
        isOpen={isEditModalOpen}
        onClose={closeEditCandidate}
        onSave={handleCandidateUpdate}
      />

      {/* Update Candidate Status Modal */}
      <UpdateCandidateModal
        candidate={selectedCandidate}
        isOpen={isUpdateModalOpen}
        onClose={closeUpdateCandidate}
        onUpdate={handleCandidateStatusUpdate}
      />

      {/* Share Modal */}
      <Dialog open={isShareModalOpen} onOpenChange={setIsShareModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Candidates Details</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Select All Fields Checkbox */}
            <div className="flex items-center space-x-2 border-b pb-4">
              <input
                type="checkbox"
                id="select-all-fields"
                checked={selectAllFields}
                onChange={(e) => handleSelectAllFields(e.target.checked)}
                className="h-4 w-4"
              />
              <Label htmlFor="select-all-fields" className="text-lg font-semibold">
                Select All Fields
              </Label>
            </div>

            {/* Candidate Details */}
            {shareSelectedCandidates.map((candidate, index) => (
              <div key={candidate.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">
                    {candidate.firstName} {candidate.lastName} ({candidate.id}) - Client: {candidate.client}
                  </h3>
                  <button
                    onClick={() => setIsShareModalOpen(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                {/* Field Selection Table */}
                <div className="grid grid-cols-1 gap-2 max-h-60 overflow-y-auto">
                  {getAllCandidateFields().map((field) => (
                    <div key={field} className="grid grid-cols-2 gap-4 py-2 border-b">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`field-${field}-${index}`}
                          checked={selectedFields.has(field)}
                          onChange={() => handleFieldToggle(field)}
                          className="h-4 w-4"
                        />
                        <Label htmlFor={`field-${field}-${index}`} className="font-medium">
                          {field}:
                        </Label>
                      </div>
                      <div className="text-gray-700">
                        {String(candidate[field as keyof Candidate] || '-')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {/* Action Buttons */}
            <div className="flex justify-between pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setIsShareModalOpen(false)}
                className="bg-red-500 text-white hover:bg-red-600"
              >
                Close
              </Button>
              <Button
                onClick={handleShareViaEmail}
                disabled={selectedFields.size === 0}
                className="bg-green-600 text-white hover:bg-green-700 disabled:opacity-50"
              >
                Share via Email
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Email Modal */}
      <Dialog open={isEmailModalOpen} onOpenChange={setIsEmailModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Email Format</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="email-subject">Subject</Label>
              <Input
                id="email-subject"
                placeholder="Enter subject"
                value={emailForm.subject}
                onChange={(e) => setEmailForm(prev => ({ ...prev, subject: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="email-body">Body Text</Label>
              <Textarea
                id="email-body"
                placeholder="Enter subject"
                value={emailForm.body}
                onChange={(e) => setEmailForm(prev => ({ ...prev, body: e.target.value }))}
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="email-address">Email</Label>
              <Input
                id="email-address"
                placeholder="Search or add email"
                value={emailForm.email}
                onChange={(e) => setEmailForm(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>

            <div className="flex justify-between pt-4">
              <Button
                variant="outline"
                onClick={() => setIsEmailModalOpen(false)}
                className="bg-red-500 text-white hover:bg-red-600"
              >
                Cancel
              </Button>
              <Button
                onClick={handleEmailSend}
                className="bg-green-600 text-white hover:bg-green-700"
              >
                Send
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
