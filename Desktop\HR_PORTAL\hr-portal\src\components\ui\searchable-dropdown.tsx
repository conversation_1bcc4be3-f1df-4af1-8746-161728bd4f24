import { useState, useRef, useEffect } from "react";
import { ChevronDown, Plus } from "lucide-react";

interface SearchableDropdownProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  suggestions: string[];
  placeholder?: string;
  required?: boolean;
  isNewMode?: boolean;
  onToggleNewMode?: () => void;
  newModeText?: string;
  selectExistingText?: string;
  addNewText?: string;
  name: string;
  // For simple mode (like recruiters) - no toggle, just searchable input
  simpleMode?: boolean;
}

export function SearchableDropdown({
  label,
  value,
  onChange,
  suggestions,
  placeholder = "Type to search...",
  required = false,
  isNewMode = false,
  onToggleNewMode = () => {},
  newModeText = "",
  selectExistingText = "",
  addNewText = "",
  name,
  simpleMode = false,
}: SearchableDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter suggestions based on search term
  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm("");
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (isNewMode || simpleMode) {
      onChange(newValue);
      if (simpleMode) {
        setSearchTerm(newValue);
        setIsOpen(true);
      }
    } else {
      setSearchTerm(newValue);
      setIsOpen(true);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleInputFocus = () => {
    if (!isNewMode || simpleMode) {
      setIsOpen(true);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setIsOpen(false);
      setSearchTerm("");
    }
  };

  return (
    <div className="space-y-1 w-full">
      <label
        htmlFor={name}
        className="block text-sm font-medium text-gray-700"
      >
        * {label}:
      </label>

      {simpleMode ? (
        <div className="relative" ref={dropdownRef}>
          <input
            ref={inputRef}
            type="text"
            id={name}
            name={name}
            required={required}
            className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={value}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            autoComplete="off"
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 flex items-center px-2 text-gray-400"
            onClick={() => setIsOpen(!isOpen)}
          >
            <ChevronDown className="h-4 w-4" />
          </button>

          {isOpen && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
              {filteredSuggestions.length > 0 ? (
                filteredSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    type="button"
                    className="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    {suggestion}
                  </button>
                ))
              ) : (
                <div className="px-3 py-2 text-gray-500 text-sm">
                  No matches found
                </div>
              )}
            </div>
          )}
        </div>
      ) : isNewMode ? (
        <div className="space-y-2">
          <input
            ref={inputRef}
            type="text"
            id={name}
            name={name}
            required={required}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={value}
            onChange={handleInputChange}
            placeholder={placeholder}
          />
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-600">{newModeText}</span>
            <button
              type="button"
              onClick={onToggleNewMode}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {selectExistingText}
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-2" ref={dropdownRef}>
          <div className="relative">
            <input
              ref={inputRef}
              type="text"
              id={name}
              name={name}
              required={required}
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={value || searchTerm}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              autoComplete="off"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center px-2 text-gray-400"
              onClick={() => setIsOpen(!isOpen)}
            >
              <ChevronDown className="h-4 w-4" />
            </button>
            
            {isOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                {filteredSuggestions.length > 0 ? (
                  filteredSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      className="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion}
                    </button>
                  ))
                ) : (
                  <div className="px-3 py-2 text-gray-500 text-sm">
                    No matches found
                  </div>
                )}
              </div>
            )}
          </div>
          
          <button
            type="button"
            onClick={onToggleNewMode}
            className="flex items-center text-sm text-blue-600 hover:text-blue-800"
          >
            <Plus className="h-4 w-4 mr-1" />
            {addNewText}
          </button>
        </div>
      )}
    </div>
  );
}
