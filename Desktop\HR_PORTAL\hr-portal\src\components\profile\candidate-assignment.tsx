import React, { useState, useEffect, use<PERSON>emo, useRef } from "react";
import { Search, Check, X } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchActiveUsers, type User } from "@/store/slices/activeUsersSlice";
import { selectActiveUsers, selectActiveUsersLoading } from "@/store/selectors/activeUsersSelectors";
import { ApiService, type RecruiterCandidate } from "@/services/api";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { useDebounce } from "@/hooks/use-debounce";

// --- TYPE DEFINITIONS ---
interface FilterTag {
  column: string;
  value: string;
}

// --- CONSTANTS ---
const columns = [
  { key: "username", label: "Name", sortable: true },
  { key: "client", label: "Client", sortable: true },
  { key: "profile", label: "Profile", sortable: true },
  { key: "status", label: "Status", sortable: true },
];

const suggestionPriority: (keyof RecruiterCandidate)[] = [
  'username', 'client', 'profile', 'status'
];

// --- UTILITY FUNCTIONS ---
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case "new": return "bg-blue-100 text-blue-800";
    case "screening": return "bg-purple-100 text-purple-800";
    case "interview":
    case "interviewing": return "bg-amber-100 text-amber-800";
    case "offer": return "bg-emerald-100 text-emerald-800";
    case "hired": return "bg-green-100 text-green-800";
    case "rejected": return "bg-red-100 text-red-800";
    case "contacted": return "bg-indigo-100 text-indigo-800";
    case "on hold":
    case "hold": return "bg-orange-100 text-orange-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

// --- MAIN COMPONENT ---
export function CandidateAssignment() {
  const dispatch = useAppDispatch();
  const activeUsers = useAppSelector(selectActiveUsers);
  const usersLoading = useAppSelector(selectActiveUsersLoading);

  // State for Recruiters
  const [selectedRecruiter, setSelectedRecruiter] = useState<User | null>(null);
  const [recruiterSearch, setRecruiterSearch] = useState("");
  const [assignRecruiter, setAssignRecruiter] = useState<User | null>(null);
  const [assignRecruiterSearch, setAssignRecruiterSearch] = useState("");

  // State for Candidates
  const [candidates, setCandidates] = useState<RecruiterCandidate[]>([]);
  const [candidatesLoading, setCandidatesLoading] = useState(false);
  const [selectedCandidates, setSelectedCandidates] = useState<Set<number>>(new Set());

  // State for Advanced Search
  const [searchTags, setSearchTags] = useState<FilterTag[]>([]);
  const [appliedTags, setAppliedTags] = useState<FilterTag[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<{ value: string; column: string }[]>([]);
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // State for Table
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortConfig, setSortConfig] = useState<{ key: keyof RecruiterCandidate | null; direction: "ascending" | "descending" | null }>({ key: null, direction: null });
  const { isLoading: tableLoading, animateSorting, animatePagination } = useTableAnimation();

  // --- DATA & DERIVED STATE ---
  useEffect(() => {
    dispatch(fetchActiveUsers());
  }, [dispatch]);

  const recruiters = useMemo(() =>
    activeUsers.filter(user => (user.userType === 'recruiter' || user.userType === 'management') && user.isActive),
    [activeUsers]
  );

  // Debounced search values
  const debouncedRecruiterSearch = useDebounce(recruiterSearch, 250);
  const debouncedAssignRecruiterSearch = useDebounce(assignRecruiterSearch, 250);
  const debouncedInputValue = useDebounce(inputValue, 250);

  const filteredSourceRecruiters = useMemo(() =>
    recruiters.filter(r => `${r.firstName} ${r.lastName}`.toLowerCase().includes(debouncedRecruiterSearch.toLowerCase())),
    [recruiters, debouncedRecruiterSearch]
  );

  const filteredDestRecruiters = useMemo(() =>
    recruiters.filter(r => `${r.firstName} ${r.lastName}`.toLowerCase().includes(debouncedAssignRecruiterSearch.toLowerCase())),
    [recruiters, debouncedAssignRecruiterSearch]
  );

  // --- MEMOIZED DATA PIPELINE for Candidates ---
  const filteredCandidates = useMemo(() => {
    const groupedTags = appliedTags.reduce((acc, tag) => {
      const key = tag.column;
      if (!acc[key]) acc[key] = [];
      acc[key].push(tag);
      return acc;
    }, {} as Record<string, FilterTag[]>);

    if (appliedTags.length === 0) {
        return candidates;
    }

    return candidates.filter(candidate => {
      return Object.values(groupedTags).every(tagGroup =>
        tagGroup.some(tag => {
          const tagValue = tag.value.toLowerCase();
          const columnInfo = columns.find(c => c.label === tag.column);

          if (tag.column === 'Any') {
            return Object.values(candidate).some(val => String(val).toLowerCase().includes(tagValue));
          }
          if (!columnInfo) return false;

          const candidateValue = candidate[columnInfo.key as keyof RecruiterCandidate];
          return String(candidateValue).toLowerCase().includes(tagValue);
        })
      );
    });
  }, [candidates, appliedTags]);

  const sortedCandidates = useMemo(() => {
    return [...filteredCandidates].sort((a, b) => {
      if (!sortConfig.key) return 0;
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      if (aValue < bValue) return sortConfig.direction === 'ascending' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'ascending' ? 1 : -1;
      return 0;
    });
  }, [filteredCandidates, sortConfig]);

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentCandidates = useMemo(() => {
    return sortedCandidates.slice(indexOfFirstItem, indexOfLastItem);
  }, [sortedCandidates, indexOfFirstItem, indexOfLastItem]);

  // --- SUGGESTIONS EFFECT ---
  useEffect(() => {
    if (debouncedInputValue) {
      const newSuggestions: { value: string; column: string }[] = [];
      const addedSuggestions = new Set<string>();
      for (const key of suggestionPriority) {
        if (newSuggestions.length >= 7) break;
        const columnInfo = columns.find(c => c.key === key);
        if (!columnInfo) continue;
        for (const candidate of filteredCandidates) {
          if (newSuggestions.length >= 7) break;
          const value = candidate[key];
          const suggestionKey = `${value}|${columnInfo.label}`;
          if (value && typeof value === 'string' && value.toLowerCase().includes(debouncedInputValue.toLowerCase())) {
            if (!addedSuggestions.has(suggestionKey)) {
              newSuggestions.push({ value, column: columnInfo.label });
              addedSuggestions.add(suggestionKey);
            }
          }
        }
      }
      setSuggestions(newSuggestions);
    } else {
      setSuggestions([]);
    }
  }, [debouncedInputValue, filteredCandidates]);

  // --- HANDLERS ---
  const handleRecruiterSelect = async (recruiter: User) => {
    setSelectedRecruiter(recruiter);
    setRecruiterSearch(`${recruiter.firstName} ${recruiter.lastName}`);
    setCandidatesLoading(true);
    setCandidates([]);
    setSelectedCandidates(new Set());
    try {
      const fullName = `${recruiter.firstName} ${recruiter.lastName}`;
      const candidatesData = await ApiService.fetchRecruiterCandidates(fullName);
      setCandidates(candidatesData);
    } catch (error) {
      console.error('Failed to fetch candidates:', error);
    } finally {
      setCandidatesLoading(false);
    }
  };

  const handleAssignRecruiterSelect = (recruiter: User) => {
    setAssignRecruiter(recruiter);
    setAssignRecruiterSearch(`${recruiter.firstName} ${recruiter.lastName}`);
  };

  const handleSelectAll = () => {
    if (selectedCandidates.size === filteredCandidates.length && filteredCandidates.length > 0) {
      setSelectedCandidates(new Set());
    } else {
      setSelectedCandidates(new Set(filteredCandidates.map(c => c.id)));
    }
  };

  const handleCandidateSelect = (candidateId: number) => {
    setSelectedCandidates(prev => {
      const newSet = new Set(prev);
      if (newSet.has(candidateId)) {
        newSet.delete(candidateId);
      } else {
        newSet.add(candidateId);
      }
      return newSet;
    });
  };

  const handleAssignCandidates = () => {
    if (selectedCandidates.size === 0 || !assignRecruiter) {
      alert('Please select candidates and a destination recruiter.');
      return;
    }
    console.log(`Assigning ${selectedCandidates.size} candidates to ${assignRecruiter.username}`);
    alert(`Assigned ${selectedCandidates.size} candidates to ${assignRecruiter.firstName} ${assignRecruiter.lastName}`);
    setSelectedCandidates(new Set());
  };

  const handleSort = async (key: keyof RecruiterCandidate) => {
      await animateSorting();
      let direction: "ascending" | "descending" | null = "ascending";
      if (sortConfig.key === key && sortConfig.direction === "ascending") direction = "descending";
      else if (sortConfig.key === key && sortConfig.direction === "descending") direction = null;
      setSortConfig({ key, direction });
  };
  
  const paginate = async (pageNumber: number) => {
      await animatePagination();
      setCurrentPage(pageNumber);
  };

  const handleAddTag = (tagOrSuggestion: string | { value: string; column: string }) => {
    const newTag: FilterTag = typeof tagOrSuggestion === 'string' ? { value: tagOrSuggestion.trim(), column: 'Any' } : { value: (tagOrSuggestion.value || '').trim(), column: tagOrSuggestion.column };
    if (newTag.value && !searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
      setSearchTags(prev => [...prev, newTag]);
    }
    setInputValue('');
    setSuggestions([]);
  };

  const handleRemoveTag = (tagToRemove: FilterTag) => {
    setSearchTags(prev => prev.filter(tag => tag.value !== tagToRemove.value || tag.column !== tagToRemove.column));
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleApplyFilters = () => {
      setAppliedTags(searchTags);
      setCurrentPage(1);
  };
  
  const handleClearAllFilters = () => {
      setSearchTags([]);
      setAppliedTags([]);
      setInputValue('');
      setCurrentPage(1);
  };

  // --- RENDER ---
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-4">
      {/* Left Column: Candidates List */}
      <div className="lg:col-span-2 flex flex-col gap-3">
        <Card>
          <CardHeader>
            <CardTitle >Candidates of: {selectedRecruiter ? `${selectedRecruiter.firstName} ${selectedRecruiter.lastName}` : 'No Recruiter Selected'}</CardTitle>
          </CardHeader>
          <CardContent>
            {/* --- ADVANCED SEARCH BAR --- */}
            <div className="mb-3 -mt-4" ref={searchContainerRef}>
              <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500 relative">
                <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
                {searchTags.map(tag => (
                  <span key={`${tag.column}-${tag.value}`} className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    <span className="font-normal text-blue-600 mr-1">{tag.column}:</span>
                    {tag.value}
                    <button onClick={() => handleRemoveTag(tag)} className="ml-1.5 -mr-1 p-0.5 rounded-full text-blue-500 hover:bg-blue-200"><X className="h-3.5 w-3.5" /></button>
                  </span>
                ))}
                <input type="text" className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none" placeholder="Search candidates..." value={inputValue} onChange={handleInputChange} onKeyDown={(e) => { if (e.key === "Enter" && inputValue.trim()) { e.preventDefault(); handleAddTag(inputValue); } }} />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
                  <button onClick={handleApplyFilters} title="Apply Filters" className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600"><Check className="h-5 w-5" /></button>
                  <button onClick={handleClearAllFilters} title="Clear All Filters" className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500"><X className="h-5 w-5" /></button>
                </div>
              </div>
              {suggestions.length > 0 && (
                <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {suggestions.map((s, i) => (
                    <div key={`${s.value}-${i}`} className="cursor-pointer p-3 hover:bg-blue-50 flex justify-between items-center text-sm" onClick={() => handleAddTag(s)}>
                      <span className="font-medium text-gray-900">{s.value}</span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md font-medium">{s.column}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <AnimatedTableWrapper isLoading={candidatesLoading || tableLoading} loadingComponent={<TableSkeleton rows={8} cols={columns.length + 1} />} className="border rounded-lg">
              <div className="overflow-auto h-[400px]">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="p-3 text-left w-12"><input type="checkbox" onChange={handleSelectAll} checked={selectedCandidates.size > 0 && selectedCandidates.size === filteredCandidates.length} /></th>
                      {columns.map(col => (
                        <th key={col.key} className="p-3 text-left font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort(col.key as keyof RecruiterCandidate)}>
                          <div className="flex items-center gap-1">{col.label} {col.sortable && <AnimatedSortIcon direction={sortConfig.key === col.key ? sortConfig.direction : null} active={sortConfig.key === col.key} />}</div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y">
                    {currentCandidates.map(candidate => (
                      <AnimatedTableRow key={candidate.id}>
                        <td className="p-3"><input type="checkbox" checked={selectedCandidates.has(candidate.id)} onChange={() => handleCandidateSelect(candidate.id)} /></td>
                        <td className="p-3 font-medium text-gray-900">{candidate.username}</td>
                        <td className="p-3 text-gray-600">{candidate.client}</td>
                        <td className="p-3 text-gray-600">{candidate.profile}</td>
                        <td className="p-3"><span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(candidate.status)}`}>{candidate.status}</span></td>
                      </AnimatedTableRow>
                    ))}
                  </tbody>
                </table>
              </div>
            </AnimatedTableWrapper>
            
            <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
               <div>Showing {filteredCandidates.length > 0 ? indexOfFirstItem + 1 : 0} to {Math.min(indexOfLastItem, filteredCandidates.length)} of {filteredCandidates.length} candidates</div>
               <AnimatedPagination currentPage={currentPage} totalPages={Math.ceil(filteredCandidates.length/itemsPerPage)} onPageChange={paginate} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Column: Recruiter Selection & Assignment */}
      <div className="lg:col-span-1 flex flex-col gap-3">
        <Card>
          <CardHeader><CardTitle>1. Select Source Recruiter</CardTitle></CardHeader>
          <CardContent className="space-y-2 -mt-4">
            <Input placeholder="Search source recruiter..." value={recruiterSearch} onChange={e => setRecruiterSearch(e.target.value)} />
            <ScrollArea className="h-30 border rounded-md ">
              {usersLoading && <div className="p-4 text-center text-sm text-gray-500">Loading...</div>}
              {filteredSourceRecruiters.map(r => (
                <div key={r.id} className="p-2 hover:bg-gray-100 cursor-pointer text-sm" onClick={() => handleRecruiterSelect(r)}>
                  {r.firstName} {r.lastName}
                </div>
              ))}
            </ScrollArea>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader><CardTitle>2. Assign to Destination Recruiter</CardTitle></CardHeader>
          <CardContent className="space-y-2 -mt-4">
            <p className="text-sm text-gray-600">{selectedCandidates.size} candidate(s) selected.</p>
            <Input placeholder="Search destination recruiter..." value={assignRecruiterSearch} onChange={e => setAssignRecruiterSearch(e.target.value)} />
            <ScrollArea className="h-30 border rounded-md">
               {filteredDestRecruiters.map(r => (
                <div key={r.id} className="p-2 hover:bg-gray-100 cursor-pointer text-sm" onClick={() => handleAssignRecruiterSelect(r)}>
                  {r.firstName} {r.lastName}
                </div>
              ))}
            </ScrollArea>
            <Button onClick={handleAssignCandidates} disabled={selectedCandidates.size === 0 || !assignRecruiter} className="w-full  mt-3">
              Assign Candidates
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}