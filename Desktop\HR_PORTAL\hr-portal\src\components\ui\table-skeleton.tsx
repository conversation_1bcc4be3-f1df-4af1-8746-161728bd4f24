import React from 'react';

export function TableSkeleton({ rows = 8, cols = 6 }: { rows?: number; cols?: number }) {
  return (
    <div className="animate-pulse">
      <div className="overflow-hidden border border-gray-200 rounded-md">
        <div className="bg-gray-50">
          <div className="grid" style={{ gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))` }}>
            {Array.from({ length: cols }).map((_, i) => (
              <div key={i} className="h-10 bg-gray-100 border-b border-gray-200" />
            ))}
          </div>
        </div>
        <div>
          {Array.from({ length: rows }).map((_, r) => (
            <div key={r} className="grid" style={{ gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))` }}>
              {Array.from({ length: cols }).map((_, c) => (
                <div key={c} className="h-10 border-b border-gray-200">
                  <div className="h-3 w-3/5 bg-gray-200 rounded mx-3 my-3" />
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}


