import React, { useState, useEffect, useMemo, useRef } from "react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { useDebounce } from "@/hooks/use-debounce";
import { ApiService, type PeerAssignedCandidate } from "@/services/api";
import { useUser } from "@/contexts/user-context";
import { Search, Eye, Download, FileText, Calendar, ChevronDown, X, Check } from "lucide-react";
import { KebabMenu, createKebabMenuItem, type KebabMenuItem } from "@/components/ui/kebab-menu";
import { Di<PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";

// --- TYPE DEFINITIONS ---

interface PeerAssignedProfile {
  priority: string;
  date: string; // Stored as DD/MM/YYYY
  name: string;
  client: string;
  profile: string;
  skills: string;
  recruiter: string;
  reviewStatus: string;
  resume: string;
  id: number;
  job_id: number;
}

interface FilterTag {
  column: string;
  value: string;
}

// --- CONSTANTS (moved outside component for performance) ---

const columns = [
  { key: "priority", label: "Priority", sortable: true, width: "w-24" },
  { key: "date", label: "Date", sortable: true, width: "w-24" },
  { key: "name", label: "Name", sortable: true, width: "w-40" },
  { key: "client", label: "Client", sortable: true, width: "w-32" },
  { key: "profile", label: "Profile", sortable: true, width: "w-48" },
  { key: "skills", label: "Skills", sortable: true, width: "w-56" },
  { key: "recruiter", label: "Recruiter", sortable: true, width: "w-32" },
  { key: "reviewStatus", label: "Review Status", sortable: true, width: "w-36" },
  { key: "actions", label: "Actions", sortable: false, width: "w-20" },
];

const suggestionPriority: (keyof PeerAssignedProfile)[] = [
  'name', 'client', 'profile', 'skills', 'recruiter', 'reviewStatus'
];

// --- HELPER FUNCTIONS ---

const convertApiCandidateToProfile = (candidate: PeerAssignedCandidate): PeerAssignedProfile => {
  const getPriority = (status: string): string => {
    if (status.includes('Reject')) return 'Low';
    if (status.includes('Pending')) return 'High';
    return 'Medium';
  };
  const formatDate = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleDateString('en-GB'); // DD/MM/YYYY
    } catch {
      return dateString;
    }
  };
  return {
    priority: getPriority(candidate.status),
    date: formatDate(candidate.date_created),
    name: candidate.name,
    client: candidate.client,
    profile: candidate.profile,
    skills: candidate.skills,
    recruiter: candidate.recruiter,
    reviewStatus: candidate.status,
    resume: candidate.resume_present ? 'Download' : 'Not Available',
    id: candidate.id,
    job_id: candidate.job_id,
  };
};

const parseDate = (dateStr: string): Date | null => {
  if (!/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) return null;
  try {
    const [day, month, year] = dateStr.split('/').map(Number);
    return new Date(year, month - 1, day);
  } catch {
    return null;
  }
};

const formatDateForModal = (dateString: string | null): string => {
  if (!dateString) return 'Not specified';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB');
  } catch {
    return dateString;
  }
};


// --- MAIN COMPONENT ---

export default function PeerAssignedProfiles() {
  const { userName } = useUser();

  // API Data State
  const [profiles, setProfiles] = useState<PeerAssignedProfile[]>([]);
  const [candidatesData, setCandidatesData] = useState<PeerAssignedCandidate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal State
  const [selectedCandidate, setSelectedCandidate] = useState<PeerAssignedCandidate | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Table State
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortConfig, setSortConfig] = useState<{ key: string | null; direction: "ascending" | "descending" | null }>({ key: null, direction: null });

  // Advanced Search & Filter State
  const [searchTags, setSearchTags] = useState<FilterTag[]>([]);
  const [appliedTags, setAppliedTags] = useState<FilterTag[]>([]);
  const [inputValue, setInputValue] = useState('');
  const debouncedInputValue = useDebounce(inputValue, 250);
  const [suggestions, setSuggestions] = useState<{ value: string; column: string }[]>([]);
  const [dateFilter, setDateFilter] = useState<number | null>(null);
  const [appliedCustomDateRange, setAppliedCustomDateRange] = useState<{ start: string, end: string } | null>(null);

  // Refs for UI
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const dateButtonRef = useRef<HTMLButtonElement>(null);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');

  // Animation Hook
  const { isLoading: tableLoading, animateSorting, animatePagination } = useTableAnimation();

  // --- DATA FETCHING & PROCESSING ---
  useEffect(() => {
    const fetchProfiles = async () => {
      if (!userName) return;
      setLoading(true);
      try {
        const response = await ApiService.fetchPeerAssignedProfiles(userName);
        setCandidatesData(response.candidates);
        setProfiles(response.candidates.map(convertApiCandidateToProfile));
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch profiles');
      } finally {
        setLoading(false);
      }
    };
    fetchProfiles();
  }, [userName]);

  // --- MEMOIZED DATA PIPELINE ---
  const filteredData = useMemo(() => {
    const groupedTags = appliedTags.reduce((acc, tag) => {
      const key = tag.column;
      if (!acc[key]) acc[key] = [];
      acc[key].push(tag);
      return acc;
    }, {} as Record<string, FilterTag[]>);

    return profiles.filter((row) => {
      // Date Filter
      const profileDate = parseDate(row.date);
      if (!profileDate) return true; // Don't filter out rows with unparseable dates
      if (dateFilter) {
        const now = new Date();
        const pastDate = new Date();
        pastDate.setDate(now.getDate() - dateFilter);
        if (profileDate < pastDate) return false;
      }
      if (appliedCustomDateRange) {
        const startDate = new Date(appliedCustomDateRange.start);
        const endDate = new Date(appliedCustomDateRange.end);
        endDate.setDate(endDate.getDate() + 1);
        if (profileDate < startDate || profileDate >= endDate) return false;
      }

      // Tag Filter
      if (appliedTags.length > 0) {
        return Object.values(groupedTags).every(tagGroup =>
          tagGroup.some(tag => {
            const tagValue = tag.value.toLowerCase();
            const columnInfo = columns.find(c => c.label === tag.column);
            if (tag.column === 'Any') {
              return Object.values(row).some(val => String(val).toLowerCase().includes(tagValue));
            }
            if (!columnInfo) return false;
            const rowValue = row[columnInfo.key as keyof PeerAssignedProfile];
            return String(rowValue).toLowerCase().includes(tagValue);
          })
        );
      }
      return true;
    });
  }, [profiles, appliedTags, dateFilter, appliedCustomDateRange]);

  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => {
      if (!sortConfig.key) return 0;
      const aValue = a[sortConfig.key as keyof PeerAssignedProfile];
      const bValue = b[sortConfig.key as keyof PeerAssignedProfile];
      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }, [filteredData, sortConfig]);

  // Define indexes in the main component scope
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;

  const currentRows = useMemo(() => {
    // Now use the pre-calculated indexes
    return sortedData.slice(indexOfFirstItem, indexOfLastItem);
  }, [sortedData, indexOfFirstItem, indexOfLastItem]); // Update dependencies

  // --- UI EFFECTS & HANDLERS ---
  useEffect(() => {
    if (debouncedInputValue) {
      const newSuggestions: { value: string; column: string }[] = [];
      const addedSuggestions = new Set<string>();
      for (const key of suggestionPriority) {
        if (newSuggestions.length >= 7) break;
        const columnInfo = columns.find(c => c.key === key);
        if (!columnInfo) continue;
        for (const profile of filteredData) {
          if (newSuggestions.length >= 7) break;
          const value = profile[key];
          const suggestionKey = `${value}|${columnInfo.label}`;
          if (value && typeof value === 'string' && value.toLowerCase().includes(debouncedInputValue.toLowerCase())) {
            if (!addedSuggestions.has(suggestionKey)) {
              newSuggestions.push({ value, column: columnInfo.label });
              addedSuggestions.add(suggestionKey);
            }
          }
        }
      }
      setSuggestions(newSuggestions);
    } else {
      setSuggestions([]);
    }
  }, [debouncedInputValue, filteredData]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setSuggestions([]);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dateDropdownRef.current && !dateDropdownRef.current.contains(event.target as Node) && dateButtonRef.current && !dateButtonRef.current.contains(event.target as Node)) {
        setIsDateDropdownOpen(false);
      }
    }
    if (isDateDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isDateDropdownOpen]);

  const handleAddTag = (tagOrSuggestion: string | { value: string; column: string }) => {
    const newTag: FilterTag = typeof tagOrSuggestion === 'string' ? { value: tagOrSuggestion.trim(), column: 'Any' } : { value: (tagOrSuggestion.value || '').trim(), column: tagOrSuggestion.column };
    if (newTag.value && !searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
      setSearchTags(prev => [...prev, newTag]);
    }
    setInputValue('');
    setSuggestions([]);
  };

  const handleRemoveTag = (tagToRemove: FilterTag) => {
    setSearchTags(prev => prev.filter(tag => tag.value !== tagToRemove.value || tag.column !== tagToRemove.column));
  };

  const handleApplyFilters = () => {
    setAppliedTags([...searchTags]);
    setDateFilter(null);
    setAppliedCustomDateRange(null);
    setCurrentPage(1);
  };

  const handleClearAllFilters = () => {
    setSearchTags([]);
    setAppliedTags([]);
    setInputValue('');
    setDateFilter(null);
    setAppliedCustomDateRange(null);
    setCustomStartDate('');
    setCustomEndDate('');
    setCurrentPage(1);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const dateFilterOptions = [
    { value: null, label: "All Time" },
    { value: 1, label: "Last 24 Hours" },
    { value: 7, label: "Last 7 Days" },
    { value: 30, label: "Last 30 Days" },
  ];

  const handleDateFilter = (days: number | null) => {
    setAppliedTags([]);
    setSearchTags([]);
    setDateFilter(days);
    setAppliedCustomDateRange(null);
    setIsDateDropdownOpen(false);
    setCurrentPage(1);
  };

  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      setAppliedCustomDateRange({ start: customStartDate, end: customEndDate });
      setDateFilter(null);
      setAppliedTags([]);
      setSearchTags([]);
      setIsDateDropdownOpen(false);
      setCurrentPage(1);
    } else {
      alert('Please select both start and end dates');
    }
  };

  const getCurrentDateFilterLabel = () => {
    if (appliedCustomDateRange) {
      const startDate = new Date(appliedCustomDateRange.start).toLocaleDateString('en-GB');
      const endDate = new Date(appliedCustomDateRange.end).toLocaleDateString('en-GB');
      return `${startDate} - ${endDate}`;
    }
    const option = dateFilterOptions.find(opt => opt.value === dateFilter);
    return option ? option.label : "All Time";
  };

  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };

  const handleSort = async (key: string) => {
    await animateSorting();
    let direction: "ascending" | "descending" | null = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") direction = "descending";
    else if (sortConfig.key === key && sortConfig.direction === "descending") direction = null;
    setSortConfig({ key, direction });
  };

  const handleViewDetails = (candidateId: number) => {
    const candidate = candidatesData.find(c => c.id === candidateId);
    if (candidate) {
      setSelectedCandidate(candidate);
      setIsModalOpen(true);
    }
  };

  const createProfileMenuItems = (profile: PeerAssignedProfile): KebabMenuItem[] => [
    createKebabMenuItem("view-jd", "View Job Description", () => alert('View JD functionality to be implemented'), { icon: Eye }),
    createKebabMenuItem("download-resume", "Download Resume", () => {
      if (profile.resume === 'Download') alert('Resume download functionality to be implemented');
      else alert('Resume not available');
    }, { icon: Download, disabled: profile.resume !== 'Download' }
    ),
    createKebabMenuItem("view-details", "View Details", () => handleViewDetails(profile.id), { icon: FileText }),
  ];

  // --- RENDER ---
  return (
    <div className="flex flex-col mt-1 p-2">
      <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
        <div className="flex-1 min-w-[300px] relative" ref={searchContainerRef}>
          <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500">
            <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
            {searchTags.map(tag => (
              <span key={`${tag.column}-${tag.value}`} className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <span className="font-normal text-blue-600 mr-1">{tag.column}:</span>
                {tag.value}
                <button onClick={() => handleRemoveTag(tag)} className="ml-1.5 -mr-1 p-0.5 rounded-full text-blue-500 hover:bg-blue-200"><X className="h-3.5 w-3.5" /></button>
              </span>
            ))}
            <input type="text" className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none" placeholder="Search and add filters..." value={inputValue} onChange={handleInputChange} onKeyDown={(e) => { if (e.key === "Enter" && inputValue.trim()) { e.preventDefault(); handleAddTag(inputValue); } }} />
          </div>
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
            <button onClick={handleApplyFilters} title="Apply Filters" className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600"><Check className="h-5 w-5" /></button>
            <button onClick={handleClearAllFilters} title="Clear All Filters" className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500"><X className="h-5 w-5" /></button>
          </div>
          {suggestions.length > 0 && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {suggestions.map((s, i) => (
                <div key={`${s.value}-${i}`} className="cursor-pointer p-3 hover:bg-blue-50 flex justify-between items-center text-sm" onClick={() => handleAddTag(s)}>
                  <span className="font-medium text-gray-900">{s.value}</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md font-medium">{s.column}</span>
                </div>
              ))}
            </div>
          )}
        </div>
        <div className="flex flex-wrap items-center gap-3">
          <div className="relative">
            <button ref={dateButtonRef} onClick={() => setIsDateDropdownOpen(!isDateDropdownOpen)} className="bg-white border border-gray-300 text-gray-900 px-3 py-2.5 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-50 min-w-[140px] justify-between">
              <div className="flex items-center gap-2"><Calendar className="h-4 w-4" /><span>{getCurrentDateFilterLabel()}</span></div>
              <ChevronDown className={`h-4 w-4 transition-transform ${isDateDropdownOpen ? 'rotate-180' : ''}`} />
            </button>
            {isDateDropdownOpen && (
              <div ref={dateDropdownRef} className="absolute right-0 top-full mt-1 z-50 bg-white rounded-lg shadow-xl border border-gray-200 min-w-[220px] py-2">
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b">Filter by Date</div>
                {dateFilterOptions.map((option) => (
                  <button key={option.label} onClick={() => handleDateFilter(option.value)} className={`w-full text-left px-3 py-2 text-sm flex justify-between items-center hover:bg-gray-100 ${dateFilter === option.value ? 'font-bold text-blue-600' : ''}`}>
                    {option.label}
                    {dateFilter === option.value && <Check className="h-4 w-4 text-blue-600" />}
                  </button>
                ))}
                <div className="border-t mt-2 pt-2 px-3">
                  <div className="text-xs font-semibold text-gray-500 mb-2">Custom Range</div>
                  <div className="space-y-2">
                    <input type="date" value={customStartDate} onChange={(e) => setCustomStartDate(e.target.value)} className="w-full border border-gray-300 px-2 py-1 rounded text-xs" />
                    <input type="date" value={customEndDate} onChange={(e) => setCustomEndDate(e.target.value)} className="w-full border border-gray-300 px-2 py-1 rounded text-xs" />
                    <button onClick={handleCustomDateApply} disabled={!customStartDate || !customEndDate} className="w-full px-3 py-1.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50">Apply</button>
                  </div>
                </div>
              </div>
            )}
          </div>
          <select className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500" onChange={async (e) => { await animatePagination(); setItemsPerPage(Number(e.target.value)); setCurrentPage(1); }} value={itemsPerPage}>
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
          </select>
        </div>
      </div>
      <AnimatedTableWrapper isLoading={loading || tableLoading} loadingComponent={<TableSkeleton rows={8} cols={columns.length} />} className="border border-gray-200 rounded-md overflow-hidden flex-1">
        <div className="overflow-auto h-[490px]" style={{ scrollbarGutter: 'stable' }}>
          <table className="w-full table-fixed divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {columns.map((column) => (
                  <th key={column.key} scope="col" className={`sticky top-0 z-10 px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b ${column.sortable ? 'cursor-pointer' : ''} ${column.width}`} onClick={() => column.sortable && handleSort(column.key)}>
                    <div className="flex items-center gap-1">{column.label}<AnimatedSortIcon direction={sortConfig.key === column.key ? sortConfig.direction : null} active={sortConfig.key === column.key} size={14} /></div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentRows.length > 0 ? (
                currentRows.map((row, index) => (
                  <AnimatedTableRow key={`${row.id}-${index}`} index={index}>
                    {columns.map((column) => (
                      <td key={`${row.id}-${column.key}`} className="px-3 py-2 text-xs text-gray-800 font-medium">
                        {(() => {
                          switch (column.key) {
                            case 'priority': return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full font-bold ${row.priority === 'High' ? 'bg-red-100 text-red-800' : row.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>{row.priority}</span>;
                            case 'skills': return <div className="truncate" title={row.skills}>{row.skills}</div>;
                            case 'reviewStatus': return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full font-bold ${row.reviewStatus.includes('Pending') ? 'bg-yellow-100 text-yellow-800' : row.reviewStatus.includes('Reject') ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>{row.reviewStatus}</span>;
                            case 'actions': return <div className="flex items-center justify-center"><KebabMenu items={createProfileMenuItems(row)} /></div>;
                            default: return row[column.key as keyof PeerAssignedProfile];
                          }
                        })()}
                      </td>
                    ))}
                  </AnimatedTableRow>
                ))
              ) : (
                <tr><td colSpan={columns.length} className="px-3 py-3 text-center text-sm text-gray-500">{loading ? 'Loading profiles...' : error ? `Error: ${error}` : 'No profiles found'}</td></tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>Showing {filteredData.length > 0 ? indexOfFirstItem + 1 : 0} to {Math.min(indexOfFirstItem + itemsPerPage, filteredData.length)} of {filteredData.length} profiles</div>
        <AnimatedPagination currentPage={currentPage} totalPages={Math.ceil(filteredData.length / itemsPerPage)} onPageChange={paginate} />
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader><DialogTitle>Candidate Details</DialogTitle></DialogHeader>
          {selectedCandidate && (
            <div className="space-y-4 text-sm">
              <div className="grid grid-cols-2 gap-x-8 gap-y-3">
                {Object.entries({
                  "ID": selectedCandidate.id,
                  "Job ID": selectedCandidate.job_id,
                  "Name": selectedCandidate.name,
                  "Mobile": selectedCandidate.mobile,
                  "Email": selectedCandidate.email,
                  "Client": selectedCandidate.client,
                  "Current Company": selectedCandidate.current_company,
                  "Position": selectedCandidate.position,
                  "Profile": selectedCandidate.profile,
                  "Current Job Location": selectedCandidate.current_job_location,
                  "Preferred Location": selectedCandidate.preferred_job_location,
                  "Qualifications": selectedCandidate.qualifications,
                  "Total Experience": selectedCandidate.experience,
                  "Relevant Experience": selectedCandidate.relevant_experience,
                  "Current CTC": selectedCandidate.current_ctc,
                  "Expected CTC": selectedCandidate.expected_ctc,
                  "Notice Period": selectedCandidate.notice_period,
                  "LinkedIn": selectedCandidate.linkedin,
                  "Holding Offer": selectedCandidate.holding_offer,
                  "Recruiter": selectedCandidate.recruiter,
                  "Management": selectedCandidate.management,
                  "Status": selectedCandidate.status,
                  "Remarks": selectedCandidate.remarks,
                  "Last Working Date": formatDateForModal(selectedCandidate.last_working_date),
                  "Date Created": formatDateForModal(selectedCandidate.date_created),
                }).map(([label, value]) => (
                  <div key={label} className="flex justify-between border-b pb-1">
                    <span className="font-medium text-gray-600">{label}:</span>
                    <span className="text-gray-900 text-right ml-4">{String(value || 'N/A')}</span>
                  </div>
                ))}
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="font-medium text-gray-600">Skills:</span>
                  <span className="text-gray-900 text-right flex-1 ml-4">{selectedCandidate.skills || 'N/A'}</span>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}